<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Improve][Connector-V2] Support maxcompute sink writer with timestamp field type (#9234)|https://github.com/apache/seatunnel/commit/a513c495e3| dev |
|[Feature][Transform] Support define sink column type (#9114)|https://github.com/apache/seatunnel/commit/ab7119e507|2.3.11|
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve] maxcompute options (#9163)|https://github.com/apache/seatunnel/commit/fdacbae1af|2.3.11|
|[Fix][Connector-V2] Fix maxcompute write with multi parallelism (#9089)|https://github.com/apache/seatunnel/commit/9426b7ba2c|2.3.11|
|[Fix][Connector-V2] Fix maxcompute sink write date less than actual date (#8999)|https://github.com/apache/seatunnel/commit/fc942a599b|2.3.11|
|[Fix][Connector-V2] Fix maxcompute read with partition spec (#8896)|https://github.com/apache/seatunnel/commit/e62bf6c65c|2.3.10|
|[Fix][Connector-V2] Fix MaxCompute cannot get project and tableName when use schema (#8865)|https://github.com/apache/seatunnel/commit/a24fa8fef6|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Feature][Connector-V2] Support maxcompute source with multi-table (#8582)|https://github.com/apache/seatunnel/commit/0f78242923|2.3.10|
|[Fix][Connector-V2] Fixed adding table comments (#8514)|https://github.com/apache/seatunnel/commit/edca75b0d6|2.3.10|
|[Improve][Connector-V2] MaxComputeSink support create partition in savemode (#8474)|https://github.com/apache/seatunnel/commit/0b8f9de465|2.3.10|
|[Improve][Transform] Rename sql transform table name from &#x27;fake&#x27; to &#x27;dual&#x27; (#8298)|https://github.com/apache/seatunnel/commit/e6169684fb|2.3.9|
|[Feature][Connector-V2] Support MaxCompute save mode (#8277)|https://github.com/apache/seatunnel/commit/44ea675f1e|2.3.9|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Feature][Core] Rename `result_table_name`/`source_table_name` to `plugin_input/plugin_output` (#8072)|https://github.com/apache/seatunnel/commit/c7bbd322db|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Fix] Fix dead link on seatunnel connectors list url (#7453)|https://github.com/apache/seatunnel/commit/62b4f16f4e|2.3.8|
|[BugFix][Connector-V2][Maxcompute]fix:Maxcompute sink can&#x27;t map field(#7164) (#7168)|https://github.com/apache/seatunnel/commit/d5abf8f506|2.3.6|
|[Feature] Add unsupported datatype check for all catalog (#5890)|https://github.com/apache/seatunnel/commit/b9791285a0|2.3.4|
|FakeSource support generate different CatalogTable for MultipleTable (#5766)|https://github.com/apache/seatunnel/commit/a8b93805ea|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Improve][Connector] Add field name to `DataTypeConvertor` to improve error message (#5782)|https://github.com/apache/seatunnel/commit/ab60790f0d|2.3.4|
|[Improve][Test] Move MaxCompute test case file (#5786)|https://github.com/apache/seatunnel/commit/38132f5158|2.3.4|
|[Fix] Fix MaxCompute use not exist SCHEMA option (#5708)|https://github.com/apache/seatunnel/commit/ba4782a67d|2.3.4|
|[Feature] Support catalog in MaxCompute Source (#5283)|https://github.com/apache/seatunnel/commit/946d89cb95|2.3.4|
|[Bugfix][Connector-V2][maxcompute] sink commit with Block not exsits on server (#4725)|https://github.com/apache/seatunnel/commit/2760cae73c|2.3.2|
|[Bug] [Maxcompute] Fix failed to parse some maxcompute type (#3894)|https://github.com/apache/seatunnel/commit/642901f0a2|2.3.1|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector] add get source method to all source connector (#3846)|https://github.com/apache/seatunnel/commit/417178fb84|2.3.1|
|[Feature][API &amp; Connector &amp; Doc] add parallelism and column projection interface (#3829)|https://github.com/apache/seatunnel/commit/b9164b8ba1|2.3.1|
|[Feature][Connector-V2][Maxcompute] Add Maxcompute source &amp; sink connector (#3640)|https://github.com/apache/seatunnel/commit/80cf8f4e42|2.3.0|

</details>
