<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[fix][connector-mango] fix split with avgSize zero error (#9255)|https://github.com/apache/seatunnel/commit/564863b933|2.3.11|
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Fix][MongoDB] The Long type cannot handle string values in scientific notation (#8783)|https://github.com/apache/seatunnel/commit/00f550e3d0|2.3.11|
|[Improve] sink mongodb schema is not required (#8887)|https://github.com/apache/seatunnel/commit/3cfe8c12b9|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Fix][Connector-Mongodb] close MongodbClient when close MongodbReader (#8592)|https://github.com/apache/seatunnel/commit/06b2fc0e06|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Bug][connectors-v2] fix mongodb bson convert exception (#8044)|https://github.com/apache/seatunnel/commit/b222c13f2f|2.3.9|
|[Hotfix][Connector-v2] Fix the ClassCastException for connector-mongodb (#7586)|https://github.com/apache/seatunnel/commit/dc43370e8c|2.3.8|
|[Improve][Test][Connector-V2][MongoDB] Add few test cases for BsonToRowDataConverters (#7579)|https://github.com/apache/seatunnel/commit/a797041e5d|2.3.8|
|[Improve][Connector-V2][MongoDB] A BsonInt32 will be convert to a long type (#7567)|https://github.com/apache/seatunnel/commit/adf26c20c5|2.3.8|
|[Improve][Connector-V2][MongoDB] Support to convert to double from any numeric type (#6997)|https://github.com/apache/seatunnel/commit/c5159a2760|2.3.6|
|[bugfix][connector-mongodb] fix mongodb null value write (#6967)|https://github.com/apache/seatunnel/commit/c5ecda50f8|2.3.6|
|[Improve][MongoDB] Implement TableSourceFactory to create mongodb source (#5813)|https://github.com/apache/seatunnel/commit/59cccb6097|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[bugfix][mongodb] Fixed unsupported exception caused by bsonNull (#5659)|https://github.com/apache/seatunnel/commit/cab864aa4d|2.3.4|
|Support config column/primaryKey/constraintKey in schema (#5564)|https://github.com/apache/seatunnel/commit/eac76b4e50|2.3.4|
|[Hotfix] Fix com.google.common.base.Preconditions to seatunnel shade one (#5284)|https://github.com/apache/seatunnel/commit/ed5eadcf73|2.3.3|
|[Improve][Connector-v2][Mongodb]sink support transaction update/writing (#5034)|https://github.com/apache/seatunnel/commit/b1203c905e|2.3.3|
|[Hotfix][Connector-V2][Mongodb] Compatible with historical parameters (#4997)|https://github.com/apache/seatunnel/commit/31db35bee7|2.3.3|
|[Improve][Connector-v2][Mongodb]Optimize reading logic (#5001)|https://github.com/apache/seatunnel/commit/830196d8b7|2.3.3|
|[Hotfix][Connector-V2][Mongodb] Fix document error content and remove redundant code (#4982)|https://github.com/apache/seatunnel/commit/526197af67|2.3.3|
|[Feature][connector-v2][mongodb] mongodb support cdc sink (#4833)|https://github.com/apache/seatunnel/commit/cb651cd7f3|2.3.3|
|[Feature][Connector-v2][Mongodb]Refactor mongodb connector (#4620)|https://github.com/apache/seatunnel/commit/5b1a843e40|2.3.2|
|Merge branch &#x27;dev&#x27; into merge/cdc|https://github.com/apache/seatunnel/commit/4324ee1912|2.3.1|
|[Improve][Project] Code format with spotless plugin.|https://github.com/apache/seatunnel/commit/423b583038|2.3.1|
|[improve][api] Refactoring schema parse (#4157)|https://github.com/apache/seatunnel/commit/b2f573a13e|2.3.1|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector] add get source method to all source connector (#3846)|https://github.com/apache/seatunnel/commit/417178fb84|2.3.1|
|[Feature][API &amp; Connector &amp; Doc] add parallelism and column projection interface (#3829)|https://github.com/apache/seatunnel/commit/b9164b8ba1|2.3.1|
|[Improve] mongodb connector v2 add source query capability (#3697)|https://github.com/apache/seatunnel/commit/8a7fe6fcb6|2.3.1|
|[Hotfix][OptionRule] Fix option rule about all connectors (#3592)|https://github.com/apache/seatunnel/commit/226dc6a119|2.3.0|
|[Improve][Connector-V2][MongoDB] Unified exception for MongoDB source &amp; sink connector (#3522)|https://github.com/apache/seatunnel/commit/5af632e32b|2.3.0|
|[Feature][Connector V2] expose configurable options in MongoDB (#3347)|https://github.com/apache/seatunnel/commit/ffd5778efc|2.3.0|
|[Improve][all] change Log to @Slf4j (#3001)|https://github.com/apache/seatunnel/commit/6016100f12|2.3.0-beta|
|[Improve][Connector-V2] Improve mongodb connector (#2778)|https://github.com/apache/seatunnel/commit/efbf793fa5|2.2.0-beta|
|[DEV][Api] Replace SeaTunnelContext with JobContext and remove singleton pattern (#2706)|https://github.com/apache/seatunnel/commit/cbf82f755c|2.2.0-beta|
|[Feature][Connector-V2] Add mongodb connecter sink (#2694)|https://github.com/apache/seatunnel/commit/51c28a3387|2.2.0-beta|
|[Feature][Connector-V2] Add mongodb connecter source (#2596)|https://github.com/apache/seatunnel/commit/3ee8a8a619|2.2.0-beta|

</details>
