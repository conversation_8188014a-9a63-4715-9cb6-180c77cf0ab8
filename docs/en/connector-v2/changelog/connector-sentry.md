<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[improve] sentry options (#9261)|https://github.com/apache/seatunnel/commit/4a2f3fa915|2.3.11|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Hotfix][OptionRule] Fix option rule about all connectors (#3592)|https://github.com/apache/seatunnel/commit/226dc6a119|2.3.0|
|[Improve][Connector-V2][Sentry] Unified exception for sentry sink connector (#3513)|https://github.com/apache/seatunnel/commit/94b472b806|2.3.0|
|[Connector] [Dependency] Add Miss Dependency Cassandra And Change Kudu Plugin Name (#3432)|https://github.com/apache/seatunnel/commit/6ac6a0a0cd|2.3.0|
|[Feature][Sentry Sink V2] Add Sentry Sink Option Rules (#3318)|https://github.com/apache/seatunnel/commit/850f483816|2.3.0|
|[Feature][Connector-V2] Add sentry sink connector #2244 (#2584)|https://github.com/apache/seatunnel/commit/9fd40390a7|2.2.0-beta|

</details>
