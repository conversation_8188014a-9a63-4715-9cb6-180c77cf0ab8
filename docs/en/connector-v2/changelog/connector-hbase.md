<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve] hbase options (#8923)|https://github.com/apache/seatunnel/commit/b6a702b58f|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Fix][Connector-V2] Fix known directory create and delete ignore issues (#7700)|https://github.com/apache/seatunnel/commit/e2fb679577|2.3.8|
|[Feature][Connector-V2][Hbase] implement hbase catalog (#7516)|https://github.com/apache/seatunnel/commit/b978792cb1|2.3.8|
|[Feature][Connector-V2] Support multi-table sink feature for HBase (#7169)|https://github.com/apache/seatunnel/commit/025fa3bb88|2.3.8|
|[hotfix][connector-v2-hbase]fix and  optimize hbase source problem (#7148)|https://github.com/apache/seatunnel/commit/34a6b8e9f6|2.3.7|
|[Improve][hbase] The specified column is written to the specified column family (#5234)|https://github.com/apache/seatunnel/commit/49d397c61d|2.3.6|
|[feature][connector-v2-hbase-sink] Support Connector v2 HBase sink TTL data writing (#7116)|https://github.com/apache/seatunnel/commit/adafd80255|2.3.6|
|[E2E][HBase]Refactor hbase e2e (#6859)|https://github.com/apache/seatunnel/commit/1da9bd6ce4|2.3.6|
|[Connector]Add hbase source connector (#6348)|https://github.com/apache/seatunnel/commit/f108a5e658|2.3.6|
|[Feature][HbaseSink]support array data. (#6100)|https://github.com/apache/seatunnel/commit/b592014766|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Hotfix][Connector-v2][HbaseSink]Fix default timestamp (#4958)|https://github.com/apache/seatunnel/commit/3d8f3bf902|2.3.3|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector-V2][Hbase] Introduce hbase sink connector (#4049)|https://github.com/apache/seatunnel/commit/68bda94a4c|2.3.1|

</details>
