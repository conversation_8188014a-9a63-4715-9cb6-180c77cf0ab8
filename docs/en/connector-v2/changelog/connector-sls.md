<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[improve] sls options (#9260)|https://github.com/apache/seatunnel/commit/126164508b|2.3.11|
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Feature]Check Chinese comments in the code (#8319)|https://github.com/apache/seatunnel/commit/d58fce1caf|2.3.9|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Improve][Sls] Add sls sink connector、e2e、doc (#7830)|https://github.com/apache/seatunnel/commit/048c47d966|2.3.9|
|[Fix][Connector-V2] Fix some throwable error not be caught (#7657)|https://github.com/apache/seatunnel/commit/e19d73282e|2.3.8|
|[Feature][Connector-V2] add Aliyun SLS connector #3733 (#7348)|https://github.com/apache/seatunnel/commit/527c7c7b5f|2.3.7|

</details>
