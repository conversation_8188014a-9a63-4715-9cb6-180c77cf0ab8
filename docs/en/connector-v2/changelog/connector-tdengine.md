<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Connector-V2] Support multi-table sink feature for TDengine (#9215)|https://github.com/apache/seatunnel/commit/98b593f095|2.3.11|
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Fix][Connector-V2]  Fix NullPointerException when column or tag contains null value in TDengine sink (#9158)|https://github.com/apache/seatunnel/commit/a047cab546|2.3.11|
|[Fix][Connector][TDEngine] TDEngine support NCHAR type (#8411)|https://github.com/apache/seatunnel/commit/88c92ae1b1|2.3.9|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Improve][Connector-V2] Close all ResultSet after used (#7389)|https://github.com/apache/seatunnel/commit/853e973212|2.3.8|
|[Fix][Connector-tdengine] Fix sql exception and concurrentmodifyexception when connect to taos and read data|https://github.com/apache/seatunnel/commit/a18fca8006|2.3.7|
|[Bugfix][TDengine] Fix the issue of losing the driver due to multiple calls to the submit job REST API #6581 (#6596)|https://github.com/apache/seatunnel/commit/470bb97434|2.3.5|
|[improve][connector-tdengine] support read bool column from tdengine (#6025)|https://github.com/apache/seatunnel/commit/af39235ee3|2.3.4|
|[Bugfix][TDengine] Fix the degree of multiple parallelism affects driver loading (#6020)|https://github.com/apache/seatunnel/commit/b6ebbd47b2|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Improve][CheckStyle] Remove useless &#x27;SuppressWarnings&#x27; annotation of checkstyle. (#5260)|https://github.com/apache/seatunnel/commit/51c0d709ba|2.3.4|
|[Hotfix][Connector] Fixed TDengine connector using jdbc driver to cause loading error (#4598)|https://github.com/apache/seatunnel/commit/78f7989b81|2.3.2|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector-V2] add tdengine source (#2832)|https://github.com/apache/seatunnel/commit/acf4d5b1b4|2.3.1|

</details>
