<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[improve] rocketmq options (#9251)|https://github.com/apache/seatunnel/commit/4cbe3b9172| dev |
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve][Connector-V2] RocketMQ Source add message tag config (#8825)|https://github.com/apache/seatunnel/commit/5913e8c35f|2.3.10|
|[Improve][Connector-V2] Add optional flag for rocketmq connector to skip parse errors instead of failing (#8737)|https://github.com/apache/seatunnel/commit/701f17b5d4|2.3.10|
|[Improve][Connector-V2] RocketMQ Sink add message tag config (#7996)|https://github.com/apache/seatunnel/commit/97a1b00e48|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Fix][Connector-V2] Fix some throwable error not be caught (#7657)|https://github.com/apache/seatunnel/commit/e19d73282e|2.3.8|
|[Feature][Kafka] Support multi-table source read  (#5992)|https://github.com/apache/seatunnel/commit/60104602d1|2.3.6|
|[Fix][connector-rocketmq] commit a correct offset to broker &amp; reduce ThreadInterruptedException log (#6668)|https://github.com/apache/seatunnel/commit/b7480e1a89|2.3.6|
|[fix][connector-rocketmq]Fix a NPE problem when checkpoint.interval is set too small(#6624) (#6625)|https://github.com/apache/seatunnel/commit/6e0c81d492|2.3.5|
|[Test][E2E] Add thread leak check for connector (#5773)|https://github.com/apache/seatunnel/commit/1f2f3fc5f0|2.3.4|
|[Fix] [Connector] Rocketmq source startOffset greater than endOffset error (#6287)|https://github.com/apache/seatunnel/commit/cd44b5894e|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Improve][CheckStyle] Remove useless &#x27;SuppressWarnings&#x27; annotation of checkstyle. (#5260)|https://github.com/apache/seatunnel/commit/51c0d709ba|2.3.4|
|[Improve][pom] Formatting pom (#4761)|https://github.com/apache/seatunnel/commit/1d6d3815ec|2.3.2|
|[Hotfix][Connector-V2][RocketMQ] Fix rocketmq spark e2e test cases (#4583)|https://github.com/apache/seatunnel/commit/e711f6ef4c|2.3.2|
|[Feature][Connector-V2] Add rocketmq source and sink (#4007)|https://github.com/apache/seatunnel/commit/e333897552|2.3.2|

</details>
