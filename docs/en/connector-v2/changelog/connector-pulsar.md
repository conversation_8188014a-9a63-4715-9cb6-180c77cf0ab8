<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Improve][API] Make sure the table name in TablePath not be null (#7252)|https://github.com/apache/seatunnel/commit/764d8b0bc8|2.3.7|
|[Feature][Kafka] Support multi-table source read  (#5992)|https://github.com/apache/seatunnel/commit/60104602d1|2.3.6|
|[PulsarSource]Improve pulsar throughput performance. (#6234)|https://github.com/apache/seatunnel/commit/37461f4f3e|2.3.4|
|[Feature][Connector-v2][PulsarSink]Add Pulsar Sink Connector. (#4382)|https://github.com/apache/seatunnel/commit/543d2c5086|2.3.4|
|[Chore] Remove useless DeserializationFormatFactory and its implement (#5880)|https://github.com/apache/seatunnel/commit/f0511544ff|2.3.4|
|fix: update IDENTIFIER = Pulsar for pulsar-datasource on project:seatunnel-web (#5852)|https://github.com/apache/seatunnel/commit/3b6de3743e|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|Support config column/primaryKey/constraintKey in schema (#5564)|https://github.com/apache/seatunnel/commit/eac76b4e50|2.3.4|
|[Improve][CheckStyle] Remove useless &#x27;SuppressWarnings&#x27; annotation of checkstyle. (#5260)|https://github.com/apache/seatunnel/commit/51c0d709ba|2.3.4|
|[Hotfix] Fix com.google.common.base.Preconditions to seatunnel shade one (#5284)|https://github.com/apache/seatunnel/commit/ed5eadcf73|2.3.3|
|[Feature][Json-format] support read format for pulsar (#4111)|https://github.com/apache/seatunnel/commit/7d61ae93e7|2.3.2|
|[hotfix][pulsar] Fix the bug that can&#x27;t consume messages all the time. (#4125)|https://github.com/apache/seatunnel/commit/a6705cc5bf|2.3.2|
|[Feature] add cdc multiple table support &amp; fix zeta bug|https://github.com/apache/seatunnel/commit/533ff2c2fa|2.3.1|
|[hotfix][pulsar] PulsarSource consumer ack exception. (#4237)|https://github.com/apache/seatunnel/commit/9725d675da|2.3.1|
|Merge branch &#x27;dev&#x27; into merge/cdc|https://github.com/apache/seatunnel/commit/4324ee1912|2.3.1|
|[Improve][Project] Code format with spotless plugin.|https://github.com/apache/seatunnel/commit/423b583038|2.3.1|
|[Improve][Connector-v2][Pulsar] Set the name of the pulsar consumption thread. (#4182)|https://github.com/apache/seatunnel/commit/e567203f7d|2.3.1|
|[improve][api] Refactoring schema parse (#4157)|https://github.com/apache/seatunnel/commit/b2f573a13e|2.3.1|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Bug][Connector-v2][PulsarSource]Fix pulsar option topic-pattern bug. (#3989)|https://github.com/apache/seatunnel/commit/aee2c580ea|2.3.1|
|[Feature][Connector] add get source method to all source connector (#3846)|https://github.com/apache/seatunnel/commit/417178fb84|2.3.1|
|[Feature][API &amp; Connector &amp; Doc] add parallelism and column projection interface (#3829)|https://github.com/apache/seatunnel/commit/b9164b8ba1|2.3.1|
|[Improve][Connector-V2][Pulsar] Unified exception for Pulsar source &amp;… (#3590)|https://github.com/apache/seatunnel/commit/4fe9323419|2.3.0|
|[Hotfix][OptionRule] Fix option rule about all connectors (#3592)|https://github.com/apache/seatunnel/commit/226dc6a119|2.3.0|
|[Hotfix][Connector-V2][Pulsar] fix conditional options (#3504)|https://github.com/apache/seatunnel/commit/0066affacf|2.3.0|
|[Feature][Connector][pulsar] expose configurable options in Pulsar (#3341)|https://github.com/apache/seatunnel/commit/200faa7c29|2.3.0|
|[Connector] [Dependency] Add Miss Dependency Cassandra And Change Kudu Plugin Name (#3432)|https://github.com/apache/seatunnel/commit/6ac6a0a0cd|2.3.0|
|[chore] fix pulsar consumer comment error (#3356)|https://github.com/apache/seatunnel/commit/91e632c526|2.3.0|
|[Connector-V2] [ElasticSearch] Add ElasticSearch Source/Sink Factory (#3325)|https://github.com/apache/seatunnel/commit/38254e3f26|2.3.0|
|[hotfix][connector][pulsar] Fix not being able to mark #noMoreNewSplits when restoring (#2945)|https://github.com/apache/seatunnel/commit/5ad69076b3|2.3.0-beta|
|Move Handover to common module (#2877)|https://github.com/apache/seatunnel/commit/d94a874bcb|2.3.0-beta|
|[hotfix][connector-v2] fix pulsar source exceptions (#2820)|https://github.com/apache/seatunnel/commit/8ff0ba7015|2.2.0-beta|
|[#2606]Dependency management split (#2630)|https://github.com/apache/seatunnel/commit/fc047be69b|2.2.0-beta|
|[SeaTunnel]Simply seatunnel package pipeline. (#2563)|https://github.com/apache/seatunnel/commit/9d88b6221a|2.2.0-beta|
|[Improve][Connector-V2] Pulsar support user-defined schema (#2436)|https://github.com/apache/seatunnel/commit/16cabe6a35|2.2.0-beta|
|[improve][UT] Upgrade junit to 5.+ (#2305)|https://github.com/apache/seatunnel/commit/362319ff3e|2.2.0-beta|
|StateT of SeaTunnelSource should extend `Serializable` (#2214)|https://github.com/apache/seatunnel/commit/8c426ef850|2.2.0-beta|
|[doc][connector-v2] pulsar source options doc (#2128)|https://github.com/apache/seatunnel/commit/59ce8a2b32|2.2.0-beta|
|[api-draft][Optimize] Optimize module name (#2062)|https://github.com/apache/seatunnel/commit/f79e3112b1|2.2.0-beta|

</details>
