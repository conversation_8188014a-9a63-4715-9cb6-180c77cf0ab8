<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Improve] Add disable 2pc in SelectDB cloud sink (#6266)|https://github.com/apache/seatunnel/commit/aa0b2119a7|2.3.5|
|[Feature] Support nanosecond in SelectDB DateTimeV2 type (#6332)|https://github.com/apache/seatunnel/commit/a0ef5dac93|2.3.5|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[improve][SelectDB] Add a jobId to the selectDB label to distinguish between tasks (#4864)|https://github.com/apache/seatunnel/commit/84be0f9fd0|2.3.2|
|[Improve][Connector-V2][SelectDB Cloud]Refactor some SelectDB Cloud Sink code as well as support copy into batch and async flush and cdc  (#4312)|https://github.com/apache/seatunnel/commit/11e94b216f|2.3.1|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector-V2][SelectDB Cloud] Support SelectDB Cloud Sink Connector (#3958)|https://github.com/apache/seatunnel/commit/79a134a03b|2.3.1|

</details>
