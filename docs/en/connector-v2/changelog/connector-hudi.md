<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[improve] hudi options (#8952)|https://github.com/apache/seatunnel/commit/b24d0e7f86|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6eeb|2.3.10|
|[Improve][CI]skip ui module, improve module dependent (#8225)|https://github.com/apache/seatunnel/commit/81de0a69cc|2.3.9|
|[Feature][Connector-V2] Support write cdc changelog event into hudi sink (#7845)|https://github.com/apache/seatunnel/commit/934434cc75|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Feature][Connector-V2] Optimize hudi sink (#7662)|https://github.com/apache/seatunnel/commit/0d12520f91|2.3.8|
|[Improve][Connector] Add multi-table sink option check (#7360)|https://github.com/apache/seatunnel/commit/2489f6446b|2.3.7|
|[Feature][Core] Support using upstream table placeholders in sink options and auto replacement (#7131)|https://github.com/apache/seatunnel/commit/c4ca74122c|2.3.6|
|Bump org.xerial.snappy:snappy-java (#7144)|https://github.com/apache/seatunnel/commit/aa26471fb7|2.3.6|
|[Feature][Connector-V2] [Hudi]Add hudi sink connector (#4405)|https://github.com/apache/seatunnel/commit/dc271dcfb4|2.3.6|
|[Fix][Connector-V2] Fix connector support SPI but without no args constructor (#6551)|https://github.com/apache/seatunnel/commit/5f3c9c36a5|2.3.5|
|[Improve][Common] Adapt `FILE_OPERATION_FAILED` to `CommonError` (#5928)|https://github.com/apache/seatunnel/commit/b3dc0bbc21|2.3.4|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b2|2.3.4|
|[Hotfix][Zeta] Fix conflict dependency of hadoop-hdfs (#4509)|https://github.com/apache/seatunnel/commit/66923fbdbd|2.3.2|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Feature][Connector] add get source method to all source connector (#3846)|https://github.com/apache/seatunnel/commit/417178fb84|2.3.1|
|[Feature][API &amp; Connector &amp; Doc] add parallelism and column projection interface (#3829)|https://github.com/apache/seatunnel/commit/b9164b8ba1|2.3.1|
|[Feature][Connector V2] expose configurable options in Hudi (#3383)|https://github.com/apache/seatunnel/commit/fd4cec3a95|2.3.0|
|fix hudi connector v2 compile error. (#3728)|https://github.com/apache/seatunnel/commit/4fba0aa024|2.3.0|
|[Improve][Connector-V2][Hudi] Unified exception for hudi source connector (#3581)|https://github.com/apache/seatunnel/commit/b2fda11ddc|2.3.0|
|[bug][Connector-V2][Hudi] HashCode may be negative (#3184)|https://github.com/apache/seatunnel/commit/8beffbb603|2.3.0|
|[DEV][Api] Replace SeaTunnelContext with JobContext and remove singleton pattern (#2706)|https://github.com/apache/seatunnel/commit/cbf82f755c|2.2.0-beta|
|[#2606]Dependency management split (#2630)|https://github.com/apache/seatunnel/commit/fc047be69b|2.2.0-beta|
|[improve][UT] Upgrade junit to 5.+ (#2305)|https://github.com/apache/seatunnel/commit/362319ff3e|2.2.0-beta|
|StateT of SeaTunnelSource should extend `Serializable` (#2214)|https://github.com/apache/seatunnel/commit/8c426ef850|2.2.0-beta|
|[Connector-V2] Add Hive sink connector v2 (#2158)|https://github.com/apache/seatunnel/commit/23ad4ee735|2.2.0-beta|
|[Connector-V2]Add Hudi Source (#2147)|https://github.com/apache/seatunnel/commit/eaedc0a3c7|2.2.0-beta|

</details>
