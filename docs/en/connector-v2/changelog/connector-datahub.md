<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Connector-V2] Make some sink parameters optional for DataHub  (#9229)|https://github.com/apache/seatunnel/commit/7418fae10c|2.3.11|
|[Feature][Connector-V2] Datahub support multi-table sink (#9212)|https://github.com/apache/seatunnel/commit/7027162dec|2.3.11|
|[improve] datahub sink options (#8744)|https://github.com/apache/seatunnel/commit/88f35bd705|2.3.10|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de7408100|2.3.4|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd601051|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab166561|2.3.1|
|[Hotfix][OptionRule] Fix option rule about all connectors (#3592)|https://github.com/apache/seatunnel/commit/226dc6a119|2.3.0|
|[Improve][Connector-V2][DataHub] Unified exception for DataHub sink connector &amp; change package name of DataHub (#3446)|https://github.com/apache/seatunnel/commit/395635fa18|2.3.0|
|[improve][connector] The Factory#factoryIdentifier must be consistent with PluginIdentifierInterface#getPluginName (#3328)|https://github.com/apache/seatunnel/commit/d9519d696a|2.3.0|
|[Improve][Connector-V2][DataHub] Add DataHub Sink Factory (#3323)|https://github.com/apache/seatunnel/commit/685978d061|2.3.0|
|[#2606]Dependency management split (#2630)|https://github.com/apache/seatunnel/commit/fc047be69b|2.2.0-beta|
|[Feature][Connector-V2]Support datahub sink  (#2558)|https://github.com/apache/seatunnel/commit/43600a7049|2.2.0-beta|

</details>
