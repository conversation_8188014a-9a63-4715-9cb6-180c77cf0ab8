<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Feature][Transform] Support define sink column type (#9114)|https://github.com/apache/seatunnel/commit/ab7119e507|2.3.11|
|[Feature][Checkpoint] Add check script for source/sink state class serialVersionUID missing (#9118)|https://github.com/apache/seatunnel/commit/4f5adeb1c7|2.3.11|
|[improve] milvus options (#9165)|https://github.com/apache/seatunnel/commit/5247e17640|2.3.11|
|[Fix][Connector-V2] Fix load state check in MilvusSourceReader to consider partition-level status (#8937)|https://github.com/apache/seatunnel/commit/bde235090b|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef800016|2.3.9|
|[Improve][Core] Refactor common options of column/row (#7911)|https://github.com/apache/seatunnel/commit/d1582afee6|2.3.9|
|[Feature] [connector-milvus] update milvus connector to support dynamic schema, failed retry, etc. (#7885)|https://github.com/apache/seatunnel/commit/6a31f91729|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03c|2.3.9|
|[Fix][Connector-V2] Fix known directory create and delete ignore issues (#7700)|https://github.com/apache/seatunnel/commit/e2fb679577|2.3.8|
|[Improve][Connector-V2] Optimize milvus code (#7691)|https://github.com/apache/seatunnel/commit/1eddb8e1b1|2.3.8|
|[Improve] [Connector-V2] Optimize milvus-connector config code (#7658)|https://github.com/apache/seatunnel/commit/f831f7a5ec|2.3.8|
|[Improve][Connector-V2] update vectorType (#7446)|https://github.com/apache/seatunnel/commit/1bba72385b|2.3.8|
|[Improve][API] Move catalog open to SaveModeHandler (#7439)|https://github.com/apache/seatunnel/commit/8c2c5c79a1|2.3.8|
|[Feature][Connector-V2] Fake Source support produce vector data (#7401)|https://github.com/apache/seatunnel/commit/6937d10ac3|2.3.8|
|[Feature][Connector-V2][Milvus] Support Milvus source &amp; sink (#7158)|https://github.com/apache/seatunnel/commit/0c69b9166e|2.3.6|

</details>
